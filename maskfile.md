# I2R Network Manager commands

## run

### run embedded-hal

> <PERSON><PERSON><PERSON><PERSON> le embedded-hal

```sh
embedded-hal mock
```

### run i2r-network

> Démarre i2r-network

```sh
cargo run --bin i2r-network -- mock
```

### run i2r-networkWithSocket

> Démarre i2r-network

```sh
mkdir -p /tmp/fk_sysstd_w
rm -f /tmp/fk_sysstd_w/noty_net
socat -u UNIX-RECV:/tmp/fk_sysstd_w/noty_net STDOUT &
SOCAT_PID=$! 
NOTIFY_SOCKET=/tmp/fk_sysstd_w/noty_net WATCHDOG_USEC=10000000 cargo run --bin i2r-network -- mock
kill SOCAT_PID
```

## test

> Lance les tests du projet

```sh
cargo nextest run
```

## build

> Construit le binaire de i2r-network

```sh
cargo build --release
upx --best --lzma target/release/i2r-network
```

## generate

> G<PERSON><PERSON> le code pour s'interfacer au HAL par DBUS

```sh
rm -rf /tmp/dbus-api
git clone -q ssh://*************************:31029/dev/nexus/ccma/i2r/dbus-api.git /tmp/dbus-api

dbus-codegen-rust --file /tmp/dbus-api/src/main/dbus/specs/fr.enedis.HAL.DoorManager1.xml --output crates/dbus-api/src/door_manager.rs  --client nonblock
dbus-codegen-rust --file /tmp/dbus-api/src/main/dbus/specs/fr.enedis.HAL.LEDManager1.xml --output crates/dbus-api/src/led_manager.rs  --client nonblock
dbus-codegen-rust --file /tmp/dbus-api/src/main/dbus/specs/fr.enedis.HAL.ModemManager1.xml --output crates/dbus-api/src/modem_manager.rs  --client nonblock 
dbus-codegen-rust --file /tmp/dbus-api/src/main/dbus/specs/fr.enedis.HAL.PartitionManager1.xml --output crates/dbus-api/src/partition_manager.rs  --client nonblock
dbus-codegen-rust --file /tmp/dbus-api/src/main/dbus/specs/fr.enedis.HAL.ServicesDBUS1.xml --output crates/dbus-api/src/services_dbus.rs  --client nonblock
dbus-codegen-rust --file /tmp/dbus-api/src/main/dbus/specs/fr.enedis.HAL.BoardManager1.xml --output crates/dbus-api/src/board_manager.rs  --client nonblock
```