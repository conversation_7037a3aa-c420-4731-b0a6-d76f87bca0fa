ARG RUST_VERSION=1.75.0
ARG NODE_VERSION=v20.15.0

# Le hostname d'artifactory à utiliser
ARG ARTIFACTORY_URL=artifactory-zci.enedis.fr

# L'URL du registre
ARG REGISTRY_URL=delivere-docker-stages.${ARTIFACTORY_URL}

FROM ${REGISTRY_URL}/embedded-hal:bin AS embedded-hal

FROM ${REGISTRY_URL}/i2r-rust:base-${RUST_VERSION} AS base

FROM base AS dev
ARG RUST_VERSION
ARG NODE_VERSION
ARG ARTIFACTORY_URL

USER wudi
COPY --from=embedded-hal --chown=wudi:wudi /app/embedded-hal /home/<USER>/.local/bin/

ENV PATH=/home/<USER>/.local/bin:$PATH

RUN sudo apt update && sudo apt install -y -q --no-install-recommends \
    socat \
    && sudo apt clean && sudo rm -rf /var/lib/apt/lists/*

FROM base AS build
USER root

RUN mkdir /app && chown wudi:wudi /app

USER wudi
WORKDIR /app

COPY --chown=wudi:wudi . .

RUN mask build

FROM base AS bin
USER root
RUN mkdir /app
COPY --from=build /app/target/release/i2r-network /app/i2r-network